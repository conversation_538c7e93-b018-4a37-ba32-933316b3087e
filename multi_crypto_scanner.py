#!/usr/bin/env python3
"""
سكريبت متقدم لفحص محافظ العملات المشفرة المتعددة
Advanced Multi-Cryptocurrency Wallet Scanner
"""

import os
import random
import hashlib
import logging
import time
import requests
import json
from typing import Optional, Dict, Any, List
from datetime import datetime
import threading
from concurrent.futures import ThreadPoolExecutor

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('multi_crypto_scanner.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MultiCryptoScanner:
    """فئة لفحص محافظ العملات المشفرة المتعددة"""
    
    def __init__(self):
        self.base58_alphabet = "**********************************************************"
        self.found_wallets = []
        self.scan_stats = {
            'total_scanned': 0,
            'bitcoin_scanned': 0,
            'ethereum_scanned': 0,
            'found_with_balance': 0,
            'start_time': time.time()
        }
        
        # إعدادات APIs
        self.api_endpoints = {
            'bitcoin': [
                'https://blockchain.info/rawaddr/',
                'https://blockstream.info/api/address/'
            ],
            'ethereum': [
                'https://api.etherscan.io/api?module=account&action=balance&address=',
            ]
        }
    
    def generate_bitcoin_wallet(self) -> Dict[str, str]:
        """توليد محفظة بيتكوين عشوائية"""
        # توليد مفتاح خاص عشوائي
        private_key_bytes = os.urandom(32)
        private_key_hex = private_key_bytes.hex()
        
        # تحويل إلى WIF
        wif = self.private_key_to_wif(private_key_hex)
        
        # توليد العنوان
        public_key = self.private_key_to_public_key(private_key_hex)
        address = self.public_key_to_bitcoin_address(public_key)
        
        return {
            'type': 'bitcoin',
            'address': address,
            'private_key': private_key_hex,
            'wif': wif,
            'public_key': public_key
        }
    
    def generate_ethereum_wallet(self) -> Dict[str, str]:
        """توليد محفظة إيثريوم عشوائية"""
        # توليد مفتاح خاص عشوائي
        private_key_bytes = os.urandom(32)
        private_key_hex = private_key_bytes.hex()
        
        # توليد العنوان (مبسط)
        address = self.private_key_to_ethereum_address(private_key_hex)
        
        return {
            'type': 'ethereum',
            'address': address,
            'private_key': private_key_hex
        }
    
    def private_key_to_wif(self, private_key_hex: str) -> str:
        """تحويل المفتاح الخاص إلى تنسيق WIF"""
        extended_key = "80" + private_key_hex
        first_hash = hashlib.sha256(bytes.fromhex(extended_key)).digest()
        second_hash = hashlib.sha256(first_hash).digest()
        checksum = second_hash[:4].hex()
        final_key = extended_key + checksum
        return self.base58_encode(bytes.fromhex(final_key))
    
    def base58_encode(self, data: bytes) -> str:
        """ترميز البيانات بتنسيق Base58"""
        num = int.from_bytes(data, 'big')
        encoded = ""
        
        while num > 0:
            num, remainder = divmod(num, 58)
            encoded = self.base58_alphabet[remainder] + encoded
        
        for byte in data:
            if byte == 0:
                encoded = '1' + encoded
            else:
                break
                
        return encoded
    
    def private_key_to_public_key(self, private_key_hex: str) -> str:
        """تحويل المفتاح الخاص إلى مفتاح عام"""
        hash_obj = hashlib.sha256(bytes.fromhex(private_key_hex))
        public_key = "04" + hash_obj.hexdigest() + hashlib.sha256(hash_obj.digest()).hexdigest()
        return public_key[:130]
    
    def public_key_to_bitcoin_address(self, public_key: str) -> str:
        """تحويل المفتاح العام إلى عنوان Bitcoin"""
        sha256_hash = hashlib.sha256(bytes.fromhex(public_key)).digest()
        ripemd160 = hashlib.new('ripemd160')
        ripemd160.update(sha256_hash)
        hash160 = ripemd160.digest()
        versioned_payload = b'\x00' + hash160
        checksum = hashlib.sha256(hashlib.sha256(versioned_payload).digest()).digest()[:4]
        address_bytes = versioned_payload + checksum
        return self.base58_encode(address_bytes)
    
    def private_key_to_ethereum_address(self, private_key_hex: str) -> str:
        """تحويل المفتاح الخاص إلى عنوان Ethereum (مبسط)"""
        # هذا تنفيذ مبسط - في الواقع يتطلب مكتبة secp256k1
        hash_obj = hashlib.sha256(bytes.fromhex(private_key_hex))
        keccak_hash = hashlib.sha3_256(hash_obj.digest()).hexdigest()
        address = "0x" + keccak_hash[-40:]
        return address
    
    def check_bitcoin_balance(self, address: str) -> Optional[Dict[str, Any]]:
        """التحقق من رصيد البيتكوين"""
        try:
            # محاولة blockchain.info أولاً
            url = f"https://blockchain.info/rawaddr/{address}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                balance_satoshi = data.get('final_balance', 0)
                balance_btc = balance_satoshi / 100000000
                
                return {
                    'balance': balance_btc,
                    'balance_satoshi': balance_satoshi,
                    'total_received': data.get('total_received', 0) / 100000000,
                    'total_sent': data.get('total_sent', 0) / 100000000,
                    'transactions': data.get('n_tx', 0),
                    'currency': 'BTC',
                    'api_source': 'blockchain.info'
                }
            
            # محاولة blockstream كبديل
            url = f"https://blockstream.info/api/address/{address}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                balance_satoshi = data.get('chain_stats', {}).get('funded_txo_sum', 0) - \
                                data.get('chain_stats', {}).get('spent_txo_sum', 0)
                balance_btc = balance_satoshi / 100000000
                
                return {
                    'balance': balance_btc,
                    'balance_satoshi': balance_satoshi,
                    'total_received': data.get('chain_stats', {}).get('funded_txo_sum', 0) / 100000000,
                    'total_sent': data.get('chain_stats', {}).get('spent_txo_sum', 0) / 100000000,
                    'transactions': data.get('chain_stats', {}).get('tx_count', 0),
                    'currency': 'BTC',
                    'api_source': 'blockstream.info'
                }
                
        except Exception as e:
            logger.debug(f"خطأ في التحقق من رصيد البيتكوين: {str(e)}")
        
        return None
    
    def check_ethereum_balance(self, address: str) -> Optional[Dict[str, Any]]:
        """التحقق من رصيد الإيثريوم"""
        try:
            # استخدام etherscan API (يتطلب API key للاستخدام المكثف)
            url = f"https://api.etherscan.io/api?module=account&action=balance&address={address}&tag=latest"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == '1':
                    balance_wei = int(data.get('result', '0'))
                    balance_eth = balance_wei / 10**18
                    
                    return {
                        'balance': balance_eth,
                        'balance_wei': balance_wei,
                        'currency': 'ETH',
                        'api_source': 'etherscan.io'
                    }
                    
        except Exception as e:
            logger.debug(f"خطأ في التحقق من رصيد الإيثريوم: {str(e)}")
        
        return None
    
    def scan_single_wallet(self, wallet_type: str) -> Dict[str, Any]:
        """فحص محفظة واحدة"""
        if wallet_type == 'bitcoin':
            wallet = self.generate_bitcoin_wallet()
            balance_info = self.check_bitcoin_balance(wallet['address'])
            self.scan_stats['bitcoin_scanned'] += 1
        elif wallet_type == 'ethereum':
            wallet = self.generate_ethereum_wallet()
            balance_info = self.check_ethereum_balance(wallet['address'])
            self.scan_stats['ethereum_scanned'] += 1
        else:
            return None
        
        self.scan_stats['total_scanned'] += 1
        
        wallet_data = {
            **wallet,
            'scan_time': datetime.now().isoformat(),
            'balance_info': balance_info
        }
        
        # تسجيل النتائج
        currency = wallet_type.upper()
        logger.info(f"🔍 فحص محفظة {currency}: {wallet['address']}")
        
        if balance_info and balance_info['balance'] > 0:
            self.scan_stats['found_with_balance'] += 1
            logger.info(f"💰 تم العثور على محفظة {currency} بها رصيد!")
            logger.info(f"العنوان: {wallet['address']}")
            logger.info(f"الرصيد: {balance_info['balance']:.8f} {balance_info['currency']}")
            logger.info(f"المفتاح الخاص: {wallet['private_key']}")
            
            if wallet_type == 'bitcoin':
                logger.info(f"WIF: {wallet['wif']}")
            
            self.found_wallets.append(wallet_data)
            self.save_found_wallet(wallet_data)
        else:
            logger.info(f"❌ لا يوجد رصيد في محفظة {currency}: {wallet['address']}")
        
        return wallet_data
    
    def save_found_wallet(self, wallet_data: Dict[str, Any]):
        """حفظ المحفظة التي تحتوي على رصيد"""
        filename = f"found_wallets_{datetime.now().strftime('%Y%m%d')}.json"
        
        try:
            if os.path.exists(filename):
                with open(filename, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
            else:
                existing_data = []
            
            existing_data.append(wallet_data)
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, ensure_ascii=False, indent=2)
                
            logger.info(f"تم حفظ بيانات المحفظة في: {filename}")
            
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات المحفظة: {str(e)}")
    
    def run_multi_threaded_scan(self, max_iterations: int = None, delay: float = 1.0, 
                               threads: int = 3, wallet_types: List[str] = None):
        """تشغيل الفحص متعدد الخيوط"""
        if wallet_types is None:
            wallet_types = ['bitcoin', 'ethereum']
        
        logger.info("🚀 بدء الفحص متعدد العملات...")
        logger.info(f"أنواع المحافظ: {', '.join(wallet_types)}")
        logger.info(f"عدد الخيوط: {threads}")
        logger.info(f"التأخير: {delay} ثانية")
        
        iteration = 0
        
        try:
            with ThreadPoolExecutor(max_workers=threads) as executor:
                while True:
                    if max_iterations and iteration >= max_iterations:
                        break
                    
                    # توزيع المهام على الخيوط
                    futures = []
                    for wallet_type in wallet_types:
                        future = executor.submit(self.scan_single_wallet, wallet_type)
                        futures.append(future)
                    
                    # انتظار اكتمال المهام
                    for future in futures:
                        future.result()
                    
                    iteration += 1
                    
                    # عرض الإحصائيات كل 10 تكرارات
                    if iteration % 10 == 0:
                        self.print_stats()
                    
                    time.sleep(delay)
                    
        except KeyboardInterrupt:
            logger.info("\n⏹️ تم إيقاف الفحص بواسطة المستخدم")
        except Exception as e:
            logger.error(f"خطأ في الفحص: {str(e)}")
        finally:
            self.print_final_summary()
    
    def print_stats(self):
        """طباعة الإحصائيات الحالية"""
        elapsed_time = time.time() - self.scan_stats['start_time']
        rate = self.scan_stats['total_scanned'] / elapsed_time * 60
        
        logger.info(f"\n📊 الإحصائيات الحالية:")
        logger.info(f"   - إجمالي المحافظ: {self.scan_stats['total_scanned']}")
        logger.info(f"   - محافظ البيتكوين: {self.scan_stats['bitcoin_scanned']}")
        logger.info(f"   - محافظ الإيثريوم: {self.scan_stats['ethereum_scanned']}")
        logger.info(f"   - المحافظ الموجودة: {self.scan_stats['found_with_balance']}")
        logger.info(f"   - معدل الفحص: {rate:.2f} محفظة/دقيقة")
    
    def print_final_summary(self):
        """طباعة الملخص النهائي"""
        elapsed_time = time.time() - self.scan_stats['start_time']
        
        logger.info("\n" + "="*60)
        logger.info("📋 الملخص النهائي - فحص العملات المتعددة")
        logger.info("="*60)
        logger.info(f"إجمالي المحافظ المفحوصة: {self.scan_stats['total_scanned']}")
        logger.info(f"محافظ البيتكوين: {self.scan_stats['bitcoin_scanned']}")
        logger.info(f"محافظ الإيثريوم: {self.scan_stats['ethereum_scanned']}")
        logger.info(f"المحافظ التي تحتوي على رصيد: {self.scan_stats['found_with_balance']}")
        logger.info(f"إجمالي الوقت: {elapsed_time:.1f} ثانية")
        logger.info(f"متوسط معدل الفحص: {self.scan_stats['total_scanned']/elapsed_time*60:.2f} محفظة/دقيقة")
        
        if self.found_wallets:
            logger.info("\n💰 المحافظ الموجودة:")
            for i, wallet in enumerate(self.found_wallets, 1):
                balance_info = wallet['balance_info']
                currency = balance_info['currency']
                balance = balance_info['balance']
                logger.info(f"{i}. {wallet['address']} - {balance:.8f} {currency}")


def main():
    """الدالة الرئيسية"""
    print("🔐 سكريبت فحص محافظ العملات المشفرة المتعددة")
    print("=" * 50)
    
    scanner = MultiCryptoScanner()
    
    try:
        # إعدادات الفحص
        max_iterations = None  # None للفحص المستمر
        delay = 2.0  # تأخير بين الفحوصات
        threads = 2  # عدد الخيوط
        wallet_types = ['bitcoin', 'ethereum']  # أنواع المحافظ
        
        scanner.run_multi_threaded_scan(
            max_iterations=max_iterations,
            delay=delay,
            threads=threads,
            wallet_types=wallet_types
        )
        
    except Exception as e:
        logger.error(f"خطأ في تشغيل السكريبت: {str(e)}")


if __name__ == "__main__":
    main()
