# سكريبت فحص محافظ البلوك تشين

## الوصف
هذا السكريبت يقوم بتوليد مفاتيح خاصة عشوائية لمحافظ البيتكوين والتحقق من أرصدتها باستخدام APIs مختلفة.

## المميزات
- 🔐 توليد مفاتيح خاصة عشوائية آمنة
- 🏦 التحقق من أرصدة المحافظ باستخدام APIs متعددة
- 📊 عرض إحصائيات مفصلة في الوقت الفعلي
- 💾 حفظ المحافظ التي تحتوي على أرصدة
- 📝 نظام سجلات شامل باللغة العربية
- ⚡ فحص مستمر مع إمكانية التحكم في السرعة

## التثبيت

### 1. تثبيت Python
تأكد من تثبيت Python 3.7 أو أحدث على نظامك.

### 2. تثبيت المكتبات المطلوبة
```bash
pip install -r requirements.txt
```

## الاستخدام

### تشغيل السكريبت
```bash
python blockchain_wallet_scanner.py
```

### تخصيص الإعدادات
يمكنك تعديل الإعدادات في الدالة `main()`:

```python
# عدد التكرارات (None للفحص المستمر)
max_iterations = 1000  # أو None

# التأخير بين الفحوصات (بالثواني)
delay = 2.0
```

## كيفية عمل السكريبت

### 1. توليد المفاتيح
- يتم توليد مفتاح خاص عشوائي (256 بت)
- تحويل المفتاح إلى تنسيق WIF
- توليد المفتاح العام والعنوان

### 2. التحقق من الأرصدة
- استخدام blockchain.info API
- استخدام blockstream.info API كبديل
- عرض معلومات مفصلة عن كل محفظة

### 3. حفظ النتائج
- حفظ المحافظ التي تحتوي على أرصدة في ملف JSON
- إنشاء سجلات مفصلة في ملف wallet_scanner.log

## الملفات المُنتجة

### wallet_scanner.log
ملف السجلات الذي يحتوي على:
- تفاصيل كل محفظة مفحوصة
- الأخطاء والتحذيرات
- الإحصائيات والتقارير

### found_wallets_YYYYMMDD.json
ملف JSON يحتوي على بيانات المحافظ التي تحتوي على أرصدة:
```json
[
  {
    "address": "**********************************",
    "private_key": "...",
    "wif": "...",
    "public_key": "...",
    "scan_time": "2024-01-01T12:00:00",
    "balance_info": {
      "balance_btc": 0.00123456,
      "balance_satoshi": 123456,
      "total_received": 0.001,
      "total_sent": 0.0,
      "transactions": 1,
      "api_source": "blockchain.info"
    }
  }
]
```

## الإحصائيات المعروضة

### أثناء التشغيل
- عدد المحافظ المفحوصة
- عدد المحافظ الموجودة
- الوقت المنقضي
- معدل الفحص (محافظ/دقيقة)

### عند العثور على محفظة
- عنوان المحفظة
- الرصيد الحالي
- إجمالي المبلغ المستلم
- عدد المعاملات
- المفتاح الخاص و WIF

## الأمان والقانونية

⚠️ **تحذير مهم:**
- هذا السكريبت للأغراض التعليمية والبحثية فقط
- احتمالية العثور على محفظة بها رصيد ضئيلة جداً (1 في 2^256)
- لا تستخدم هذا السكريبت لأغراض غير قانونية
- احترم قوانين بلدك المتعلقة بالعملات المشفرة

## المتطلبات التقنية
- Python 3.7+
- اتصال بالإنترنت للوصول إلى APIs
- مساحة تخزين كافية لملفات السجلات

## استكشاف الأخطاء

### خطأ في الاتصال بـ API
- تحقق من اتصال الإنترنت
- قد تحتاج لزيادة التأخير بين الطلبات

### استهلاك عالي للذاكرة
- قلل من عدد التكرارات
- زد التأخير بين الفحوصات

## الدعم
إذا واجهت أي مشاكل، تحقق من ملف السجلات للحصول على تفاصيل الأخطاء.
