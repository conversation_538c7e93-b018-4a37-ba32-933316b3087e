#!/usr/bin/env python3
"""
ملف إعدادات سكريبت فحص محافظ البلوك تشين
Configuration file for blockchain wallet scanner
"""

# إعدادات عامة
GENERAL_CONFIG = {
    # عدد التكرارات (None للفحص المستمر)
    'max_iterations': None,
    
    # التأخير بين الفحوصات (بالثواني)
    'scan_delay': 2.0,
    
    # عدد الخيوط للفحص المتوازي
    'thread_count': 2,
    
    # مستوى السجلات (DEBUG, INFO, WARNING, ERROR)
    'log_level': 'INFO',
    
    # حفظ السجلات في ملف
    'save_logs_to_file': True,
    
    # عرض التقدم كل N تكرار
    'progress_interval': 10
}

# إعدادات البيتكوين
BITCOIN_CONFIG = {
    # تفعيل فحص البيتكوين
    'enabled': True,
    
    # APIs المستخدمة للتحقق من الأرصدة
    'api_endpoints': [
        'https://blockchain.info/rawaddr/',
        'https://blockstream.info/api/address/'
    ],
    
    # التأخير بين طلبات API (بالثواني)
    'api_delay': 1.0,
    
    # عدد المحاولات عند فشل API
    'api_retry_count': 2,
    
    # timeout للطلبات (بالثواني)
    'api_timeout': 10
}

# إعدادات الإيثريوم
ETHEREUM_CONFIG = {
    # تفعيل فحص الإيثريوم
    'enabled': True,
    
    # APIs المستخدمة
    'api_endpoints': [
        'https://api.etherscan.io/api?module=account&action=balance&address='
    ],
    
    # مفتاح API لـ Etherscan (اختياري للاستخدام المحدود)
    'etherscan_api_key': None,
    
    # التأخير بين طلبات API
    'api_delay': 1.5,
    
    # عدد المحاولات عند فشل API
    'api_retry_count': 2,
    
    # timeout للطلبات
    'api_timeout': 10
}

# إعدادات حفظ البيانات
STORAGE_CONFIG = {
    # مجلد حفظ النتائج
    'output_directory': './results',
    
    # تنسيق اسم ملف المحافظ الموجودة
    'found_wallets_filename': 'found_wallets_{date}.json',
    
    # تنسيق اسم ملف السجلات
    'log_filename': 'wallet_scanner_{date}.log',
    
    # حفظ تفاصيل جميع المحافظ المفحوصة (حتى الفارغة)
    'save_all_scanned': False,
    
    # ضغط ملفات السجلات القديمة
    'compress_old_logs': True,
    
    # عدد أيام الاحتفاظ بالسجلات
    'log_retention_days': 30
}

# إعدادات الأمان
SECURITY_CONFIG = {
    # تشفير المفاتيح الخاصة في ملفات الحفظ
    'encrypt_private_keys': False,
    
    # كلمة مرور التشفير (إذا كان التشفير مفعلاً)
    'encryption_password': None,
    
    # حذف المفاتيح الخاصة من الذاكرة بعد الاستخدام
    'clear_memory_after_use': True,
    
    # تحذيرات الأمان
    'show_security_warnings': True
}

# إعدادات الشبكة
NETWORK_CONFIG = {
    # استخدام proxy (اختياري)
    'use_proxy': False,
    
    # إعدادات proxy
    'proxy_settings': {
        'http': None,
        'https': None
    },
    
    # User-Agent للطلبات
    'user_agent': 'Mozilla/5.0 (compatible; WalletScanner/1.0)',
    
    # عدد الاتصالات المتزامنة
    'max_concurrent_connections': 5,
    
    # إعادة المحاولة عند انقطاع الشبكة
    'retry_on_network_error': True
}

# إعدادات الإحصائيات
STATS_CONFIG = {
    # حفظ إحصائيات مفصلة
    'detailed_stats': True,
    
    # تصدير الإحصائيات إلى CSV
    'export_stats_csv': False,
    
    # إرسال إحصائيات دورية (للمراقبة)
    'periodic_stats_report': False,
    
    # فترة التقرير الدوري (بالدقائق)
    'stats_report_interval': 60
}

# إعدادات التنبيهات
NOTIFICATION_CONFIG = {
    # تفعيل التنبيهات عند العثور على محفظة
    'enable_notifications': False,
    
    # إرسال إيميل عند العثور على محفظة
    'email_notifications': False,
    
    # إعدادات الإيميل
    'email_settings': {
        'smtp_server': None,
        'smtp_port': 587,
        'username': None,
        'password': None,
        'to_email': None
    },
    
    # تنبيهات صوتية
    'sound_notifications': False,
    
    # تنبيهات سطح المكتب
    'desktop_notifications': False
}

# دالة للحصول على الإعدادات المدمجة
def get_config():
    """إرجاع جميع الإعدادات مدمجة"""
    return {
        'general': GENERAL_CONFIG,
        'bitcoin': BITCOIN_CONFIG,
        'ethereum': ETHEREUM_CONFIG,
        'storage': STORAGE_CONFIG,
        'security': SECURITY_CONFIG,
        'network': NETWORK_CONFIG,
        'stats': STATS_CONFIG,
        'notifications': NOTIFICATION_CONFIG
    }

# دالة لتحديث الإعدادات
def update_config(section, key, value):
    """تحديث قيمة إعداد معين"""
    config_sections = {
        'general': GENERAL_CONFIG,
        'bitcoin': BITCOIN_CONFIG,
        'ethereum': ETHEREUM_CONFIG,
        'storage': STORAGE_CONFIG,
        'security': SECURITY_CONFIG,
        'network': NETWORK_CONFIG,
        'stats': STATS_CONFIG,
        'notifications': NOTIFICATION_CONFIG
    }
    
    if section in config_sections and key in config_sections[section]:
        config_sections[section][key] = value
        return True
    return False

# دالة لطباعة الإعدادات الحالية
def print_current_config():
    """طباعة الإعدادات الحالية"""
    config = get_config()
    
    print("⚙️ الإعدادات الحالية:")
    print("=" * 50)
    
    for section_name, section_config in config.items():
        print(f"\n📋 {section_name.upper()}:")
        for key, value in section_config.items():
            if isinstance(value, dict):
                print(f"  {key}:")
                for sub_key, sub_value in value.items():
                    print(f"    {sub_key}: {sub_value}")
            else:
                print(f"  {key}: {value}")

if __name__ == "__main__":
    print_current_config()
