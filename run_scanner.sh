#!/bin/bash

# سكريبت تشغيل فحص محافظ البلوك تشين
# Blockchain Wallet Scanner Runner Script

echo "🔐 مرحباً بك في سكريبت فحص محافظ البلوك تشين"
echo "=================================================="

# التحقق من وجود البيئة الافتراضية
if [ ! -d "venv" ]; then
    echo "📦 إنشاء البيئة الافتراضية..."
    python3 -m venv venv
    
    echo "📥 تثبيت المكتبات المطلوبة..."
    source venv/bin/activate
    pip install -r requirements.txt
else
    echo "✅ البيئة الافتراضية موجودة"
    source venv/bin/activate
fi

echo ""
echo "اختر نوع الفحص:"
echo "1. فحص محافظ البيتكوين فقط"
echo "2. فحص محافظ متعددة العملات"
echo "3. تشغيل الاختبارات"
echo "4. فحص محدود (5 محافظ للاختبار)"
echo ""

read -p "أدخل اختيارك (1-4): " choice

case $choice in
    1)
        echo "🚀 بدء فحص محافظ البيتكوين..."
        python blockchain_wallet_scanner.py
        ;;
    2)
        echo "🚀 بدء فحص محافظ متعددة العملات..."
        python multi_crypto_scanner.py
        ;;
    3)
        echo "🧪 تشغيل الاختبارات..."
        python test_scanner.py
        ;;
    4)
        echo "🧪 فحص محدود للاختبار..."
        python -c "
from blockchain_wallet_scanner import BitcoinWalletScanner
scanner = BitcoinWalletScanner()
scanner.run_continuous_scan(max_iterations=5, delay=1.0)
"
        ;;
    *)
        echo "❌ اختيار غير صحيح"
        exit 1
        ;;
esac

echo ""
echo "✅ تم الانتهاء من التشغيل"
echo "📁 تحقق من الملفات التالية للنتائج:"
echo "   - wallet_scanner.log (سجل العمليات)"
echo "   - found_wallets_*.json (المحافظ الموجودة)"
echo "   - multi_crypto_scanner.log (سجل العملات المتعددة)"
