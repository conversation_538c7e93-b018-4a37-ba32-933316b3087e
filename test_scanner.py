#!/usr/bin/env python3
"""
سكريبت اختبار لوظائف فحص المحافظ
Test script for wallet scanning functions
"""

import logging
from blockchain_wallet_scanner import BitcoinWalletScanner
from multi_crypto_scanner import MultiCryptoScanner

# إعداد السجلات للاختبار
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_bitcoin_scanner():
    """اختبار فحص محافظ البيتكوين"""
    print("\n🧪 اختبار فحص محافظ البيتكوين")
    print("-" * 40)
    
    scanner = BitcoinWalletScanner()
    
    # اختبار توليد مفتاح خاص
    private_key = scanner.generate_random_private_key()
    print(f"✅ تم توليد مفتاح خاص: {private_key[:16]}...")
    
    # اختبار تحويل إلى WIF
    wif = scanner.private_key_to_wif(private_key)
    print(f"✅ تم تحويل إلى WIF: {wif[:16]}...")
    
    # اختبار توليد عنوان
    public_key = scanner.private_key_to_public_key(private_key)
    address = scanner.public_key_to_address(public_key)
    print(f"✅ تم توليد العنوان: {address}")
    
    # اختبار فحص واحد
    print("\n🔍 اختبار فحص محفظة واحدة...")
    wallet_data = scanner.scan_wallet()
    print(f"✅ تم فحص المحفظة: {wallet_data['address']}")
    
    return True

def test_multi_crypto_scanner():
    """اختبار فحص محافظ العملات المتعددة"""
    print("\n🧪 اختبار فحص محافظ العملات المتعددة")
    print("-" * 40)
    
    scanner = MultiCryptoScanner()
    
    # اختبار توليد محفظة بيتكوين
    btc_wallet = scanner.generate_bitcoin_wallet()
    print(f"✅ محفظة بيتكوين: {btc_wallet['address']}")
    
    # اختبار توليد محفظة إيثريوم
    eth_wallet = scanner.generate_ethereum_wallet()
    print(f"✅ محفظة إيثريوم: {eth_wallet['address']}")
    
    # اختبار فحص محفظة بيتكوين
    print("\n🔍 اختبار فحص محفظة بيتكوين...")
    btc_result = scanner.scan_single_wallet('bitcoin')
    print(f"✅ تم فحص محفظة البيتكوين: {btc_result['address']}")
    
    # اختبار فحص محفظة إيثريوم
    print("\n🔍 اختبار فحص محفظة إيثريوم...")
    eth_result = scanner.scan_single_wallet('ethereum')
    print(f"✅ تم فحص محفظة الإيثريوم: {eth_result['address']}")
    
    return True

def test_known_addresses():
    """اختبار عناوين معروفة للتأكد من عمل APIs"""
    print("\n🧪 اختبار عناوين معروفة")
    print("-" * 40)
    
    scanner = BitcoinWalletScanner()
    
    # عنوان Genesis Block للبيتكوين
    genesis_address = "**********************************"
    print(f"🔍 اختبار عنوان Genesis: {genesis_address}")
    
    balance_info = scanner.check_balance_blockchain_info(genesis_address)
    if balance_info:
        print(f"✅ تم الحصول على بيانات الرصيد:")
        print(f"   - الرصيد: {balance_info['balance_btc']:.8f} BTC")
        print(f"   - المعاملات: {balance_info['transactions']}")
        print(f"   - المصدر: {balance_info['api_source']}")
    else:
        print("❌ فشل في الحصول على بيانات الرصيد")
    
    return balance_info is not None

def run_limited_scan_test():
    """تشغيل فحص محدود للاختبار"""
    print("\n🧪 اختبار فحص محدود (5 محافظ)")
    print("-" * 40)
    
    scanner = BitcoinWalletScanner()
    
    try:
        scanner.run_continuous_scan(max_iterations=5, delay=1.0)
        print("✅ تم إكمال الفحص المحدود بنجاح")
        return True
    except Exception as e:
        print(f"❌ خطأ في الفحص المحدود: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🔬 بدء اختبارات سكريبت فحص المحافظ")
    print("=" * 50)
    
    tests = [
        ("اختبار فحص البيتكوين", test_bitcoin_scanner),
        ("اختبار فحص العملات المتعددة", test_multi_crypto_scanner),
        ("اختبار العناوين المعروفة", test_known_addresses),
        ("اختبار الفحص المحدود", run_limited_scan_test)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed += 1
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")
    
    print(f"\n{'='*50}")
    print(f"📊 نتائج الاختبارات: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
    else:
        print("⚠️ بعض الاختبارات فشلت")
    
    print("\n💡 لتشغيل الفحص الكامل:")
    print("   python blockchain_wallet_scanner.py")
    print("   python multi_crypto_scanner.py")

if __name__ == "__main__":
    main()
