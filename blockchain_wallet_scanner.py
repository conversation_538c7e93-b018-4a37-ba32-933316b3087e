#!/usr/bin/env python3
"""
سكريبت لتوليد مفاتيح البلوك تشين عشوائياً والتحقق من أرصدة المحافظ
Blockchain Wallet Scanner - Random Private Key Generator and Balance Checker
"""

import os
import random
import hashlib
import logging
import time
import requests
from typing import Optional, Dict, Any
import json
from datetime import datetime

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('wallet_scanner.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BitcoinWalletScanner:
    """فئة لتوليد مفاتيح البيتكوين عشوائياً والتحقق من أرصدتها"""
    
    def __init__(self):
        self.base58_alphabet = "**********************************************************"
        self.api_delay = 1  # تأخير بين طلبات API (ثانية)
        self.found_wallets = []
        
    def generate_random_private_key(self) -> str:
        """توليد مفتاح خاص عشوائي"""
        # توليد 32 بايت عشوائي (256 بت)
        private_key_bytes = os.urandom(32)
        private_key_hex = private_key_bytes.hex()
        
        logger.debug(f"تم توليد مفتاح خاص: {private_key_hex}")
        return private_key_hex
    
    def private_key_to_wif(self, private_key_hex: str) -> str:
        """تحويل المفتاح الخاص إلى تنسيق WIF"""
        # إضافة بادئة الشبكة الرئيسية (0x80)
        extended_key = "80" + private_key_hex
        
        # حساب checksum
        first_hash = hashlib.sha256(bytes.fromhex(extended_key)).digest()
        second_hash = hashlib.sha256(first_hash).digest()
        checksum = second_hash[:4].hex()
        
        # دمج المفتاح مع checksum
        final_key = extended_key + checksum
        
        # تحويل إلى Base58
        wif = self.base58_encode(bytes.fromhex(final_key))
        return wif
    
    def base58_encode(self, data: bytes) -> str:
        """ترميز البيانات بتنسيق Base58"""
        num = int.from_bytes(data, 'big')
        encoded = ""
        
        while num > 0:
            num, remainder = divmod(num, 58)
            encoded = self.base58_alphabet[remainder] + encoded
        
        # إضافة الأصفار البادئة
        for byte in data:
            if byte == 0:
                encoded = '1' + encoded
            else:
                break
                
        return encoded
    
    def private_key_to_public_key(self, private_key_hex: str) -> str:
        """تحويل المفتاح الخاص إلى مفتاح عام (مبسط)"""
        # هذا تنفيذ مبسط - في الواقع يتطلب مكتبة secp256k1
        # سنستخدم hash للمحاكاة
        hash_obj = hashlib.sha256(bytes.fromhex(private_key_hex))
        public_key = "04" + hash_obj.hexdigest() + hashlib.sha256(hash_obj.digest()).hexdigest()
        return public_key[:130]  # 65 بايت في hex
    
    def public_key_to_address(self, public_key: str) -> str:
        """تحويل المفتاح العام إلى عنوان Bitcoin"""
        # SHA256 hash
        sha256_hash = hashlib.sha256(bytes.fromhex(public_key)).digest()
        
        # RIPEMD160 hash
        ripemd160 = hashlib.new('ripemd160')
        ripemd160.update(sha256_hash)
        hash160 = ripemd160.digest()
        
        # إضافة بادئة الشبكة (0x00 للشبكة الرئيسية)
        versioned_payload = b'\x00' + hash160
        
        # حساب checksum
        checksum = hashlib.sha256(hashlib.sha256(versioned_payload).digest()).digest()[:4]
        
        # دمج العنوان النهائي
        address_bytes = versioned_payload + checksum
        
        # تحويل إلى Base58
        address = self.base58_encode(address_bytes)
        return address
    
    def check_balance_blockchain_info(self, address: str) -> Optional[Dict[str, Any]]:
        """التحقق من رصيد المحفظة باستخدام blockchain.info API"""
        try:
            url = f"https://blockchain.info/rawaddr/{address}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                balance_satoshi = data.get('final_balance', 0)
                balance_btc = balance_satoshi / 100000000  # تحويل من satoshi إلى BTC
                total_received = data.get('total_received', 0) / 100000000
                total_sent = data.get('total_sent', 0) / 100000000
                n_tx = data.get('n_tx', 0)
                
                return {
                    'balance_btc': balance_btc,
                    'balance_satoshi': balance_satoshi,
                    'total_received': total_received,
                    'total_sent': total_sent,
                    'transactions': n_tx,
                    'api_source': 'blockchain.info'
                }
            else:
                logger.warning(f"فشل في الحصول على البيانات من blockchain.info: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"خطأ في التحقق من الرصيد: {str(e)}")
            return None
    
    def check_balance_blockstream(self, address: str) -> Optional[Dict[str, Any]]:
        """التحقق من رصيد المحفظة باستخدام blockstream API"""
        try:
            url = f"https://blockstream.info/api/address/{address}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                balance_satoshi = data.get('chain_stats', {}).get('funded_txo_sum', 0) - \
                                data.get('chain_stats', {}).get('spent_txo_sum', 0)
                balance_btc = balance_satoshi / 100000000
                
                return {
                    'balance_btc': balance_btc,
                    'balance_satoshi': balance_satoshi,
                    'total_received': data.get('chain_stats', {}).get('funded_txo_sum', 0) / 100000000,
                    'total_sent': data.get('chain_stats', {}).get('spent_txo_sum', 0) / 100000000,
                    'transactions': data.get('chain_stats', {}).get('tx_count', 0),
                    'api_source': 'blockstream.info'
                }
            else:
                logger.warning(f"فشل في الحصول على البيانات من blockstream: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"خطأ في التحقق من الرصيد: {str(e)}")
            return None
    
    def scan_wallet(self) -> Dict[str, Any]:
        """فحص محفظة واحدة"""
        # توليد مفتاح خاص عشوائي
        private_key = self.generate_random_private_key()
        
        # تحويل إلى WIF
        wif = self.private_key_to_wif(private_key)
        
        # توليد المفتاح العام والعنوان
        public_key = self.private_key_to_public_key(private_key)
        address = self.public_key_to_address(public_key)
        
        logger.info(f"🔍 فحص المحفظة: {address}")
        logger.debug(f"المفتاح الخاص: {private_key}")
        logger.debug(f"WIF: {wif}")
        
        # التحقق من الرصيد
        balance_info = self.check_balance_blockchain_info(address)
        if balance_info is None:
            balance_info = self.check_balance_blockstream(address)
        
        wallet_data = {
            'address': address,
            'private_key': private_key,
            'wif': wif,
            'public_key': public_key,
            'scan_time': datetime.now().isoformat(),
            'balance_info': balance_info
        }
        
        if balance_info and balance_info['balance_btc'] > 0:
            logger.info(f"💰 تم العثور على محفظة بها رصيد!")
            logger.info(f"العنوان: {address}")
            logger.info(f"الرصيد: {balance_info['balance_btc']:.8f} BTC")
            logger.info(f"إجمالي المستلم: {balance_info['total_received']:.8f} BTC")
            logger.info(f"عدد المعاملات: {balance_info['transactions']}")
            logger.info(f"المفتاح الخاص: {private_key}")
            logger.info(f"WIF: {wif}")
            
            self.found_wallets.append(wallet_data)
            
            # حفظ في ملف منفصل
            self.save_found_wallet(wallet_data)
        else:
            logger.info(f"❌ لا يوجد رصيد في المحفظة: {address}")
        
        return wallet_data
    
    def save_found_wallet(self, wallet_data: Dict[str, Any]):
        """حفظ المحفظة التي تحتوي على رصيد"""
        filename = f"found_wallets_{datetime.now().strftime('%Y%m%d')}.json"
        
        try:
            # قراءة البيانات الموجودة
            if os.path.exists(filename):
                with open(filename, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
            else:
                existing_data = []
            
            # إضافة المحفظة الجديدة
            existing_data.append(wallet_data)
            
            # حفظ البيانات المحدثة
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, ensure_ascii=False, indent=2)
                
            logger.info(f"تم حفظ بيانات المحفظة في: {filename}")
            
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات المحفظة: {str(e)}")
    
    def run_continuous_scan(self, max_iterations: int = None, delay: float = 1.0):
        """تشغيل الفحص المستمر"""
        logger.info("🚀 بدء فحص المحافظ...")
        logger.info(f"التأخير بين الفحوصات: {delay} ثانية")
        
        if max_iterations:
            logger.info(f"عدد التكرارات المحدد: {max_iterations}")
        else:
            logger.info("الفحص المستمر (اضغط Ctrl+C للتوقف)")
        
        iteration = 0
        start_time = time.time()
        
        try:
            while True:
                iteration += 1
                
                logger.info(f"\n--- التكرار {iteration} ---")
                
                # فحص محفظة
                self.scan_wallet()
                
                # إحصائيات
                elapsed_time = time.time() - start_time
                rate = iteration / elapsed_time * 60  # محافظ في الدقيقة
                
                logger.info(f"📊 الإحصائيات:")
                logger.info(f"   - المحافظ المفحوصة: {iteration}")
                logger.info(f"   - المحافظ الموجودة: {len(self.found_wallets)}")
                logger.info(f"   - الوقت المنقضي: {elapsed_time:.1f} ثانية")
                logger.info(f"   - معدل الفحص: {rate:.2f} محفظة/دقيقة")
                
                # التحقق من شرط التوقف
                if max_iterations and iteration >= max_iterations:
                    logger.info(f"تم الوصول إلى العدد المحدد من التكرارات: {max_iterations}")
                    break
                
                # تأخير قبل التكرار التالي
                time.sleep(delay)
                
        except KeyboardInterrupt:
            logger.info("\n⏹️ تم إيقاف الفحص بواسطة المستخدم")
        except Exception as e:
            logger.error(f"خطأ في الفحص المستمر: {str(e)}")
        finally:
            self.print_final_summary(iteration, time.time() - start_time)
    
    def print_final_summary(self, total_scanned: int, total_time: float):
        """طباعة ملخص نهائي"""
        logger.info("\n" + "="*50)
        logger.info("📋 الملخص النهائي")
        logger.info("="*50)
        logger.info(f"إجمالي المحافظ المفحوصة: {total_scanned}")
        logger.info(f"المحافظ التي تحتوي على رصيد: {len(self.found_wallets)}")
        logger.info(f"إجمالي الوقت: {total_time:.1f} ثانية")
        logger.info(f"متوسط معدل الفحص: {total_scanned/total_time*60:.2f} محفظة/دقيقة")
        
        if self.found_wallets:
            logger.info("\n💰 المحافظ الموجودة:")
            for i, wallet in enumerate(self.found_wallets, 1):
                balance = wallet['balance_info']['balance_btc']
                logger.info(f"{i}. {wallet['address']} - {balance:.8f} BTC")


def main():
    """الدالة الرئيسية"""
    print("🔐 سكريبت فحص محافظ البلوك تشين")
    print("=" * 40)
    
    scanner = BitcoinWalletScanner()
    
    try:
        # يمكن تخصيص هذه القيم
        max_iterations = None  # None للفحص المستمر، أو رقم محدد
        delay = 2.0  # تأخير بين الفحوصات بالثواني
        
        scanner.run_continuous_scan(max_iterations=max_iterations, delay=delay)
        
    except Exception as e:
        logger.error(f"خطأ في تشغيل السكريبت: {str(e)}")


if __name__ == "__main__":
    main()
